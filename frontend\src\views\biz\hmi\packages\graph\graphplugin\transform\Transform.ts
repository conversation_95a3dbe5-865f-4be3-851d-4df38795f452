import { Dom, Graph, Node } from "@antv/x6";
import { isLine } from "../../GraphUtil";

/**
 * 扩展变换插件
 * <AUTHOR>
 * @version 1.0 2025-03-24
 */
export class TransformImpl {
  public view: any;
  public container: any;
  private options: any;
  private node: Node;
  private graph: Graph;

  constructor(options: any, node: Node, graph: Graph) {
    this.options = options;
    this.node = node;
    this.graph = graph;

    // 初始化视图和容器
    this.initializeTransform();
  }

  private initializeTransform() {
    // 获取节点视图
    this.view = this.graph.findViewByCell(this.node);

    // 创建容器元素
    this.container = document.createElement("div");
    this.container.className = "x6-widget-transform";

    // 将容器添加到图形容器中
    if (this.graph.container) {
      this.graph.container.appendChild(this.container);
    }
  }

  // 渲染节点方法
  renderHandles() {
    const view = this.view;
    if (view) {
      const node = view.cell as Node;
      // 判断是否是线
      if (isLine(node)) {
        // 线只保留2个节点
        if (this.container) {
          Dom.addClass(this.container, "line");
        }
      }
    }
  }

  // 清理方法
  dispose() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}
