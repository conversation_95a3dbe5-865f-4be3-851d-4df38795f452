export default {
  device: {
    configure: {
      selectConfigure: "Please select a configuration symbol file!",
      loading: "Loading",
      operating: "Operating",
      loadFailed: "Loading failed, reason:",
      getCustomDeviceFailed: "Failed to get custom device",
      registerDataFailed: "Data registration failed, reason:",
      variablesNotExist: "Some variables do not exist:",
      getDataError: "Data retrieval error",
      remoteSet: "Remote Set",
      remoteControlFailed: "Remote control failed, reason:",
      remoteControlSuccess: "Remote control succeeded",
      noRemoteControlType: "Remote control type not configured",
      symbolChangeReload: "Symbol changed, reloading",
      deviceChangeReload: "Device changed, reloading",
      deviceConnectReload: "Device connected successfully, reloading"
    },

    configureList: {
      searchPlaceholder: "Search by keyword",
      deviceMonitor: "Device Monitor",
      newProject: "New Project",
      addCustomComponent: "Add Custom Component",
      newConfigure: "New Configuration",
      renameProject: "Rename Project",
      deleteProject: "Delete Project",
      editConfigure: "Edit Configuration",
      renameConfigure: "Rename Configuration",
      deleteConfigure: "Delete Configuration",
      openFolder: "Open Folder",
      inputProjectName: "Please enter project name (within 10 characters, no special characters)",
      inputConfigureName: "Please enter configuration name (within 10 characters, no special characters)",
      confirm: "Confirm",
      cancel: "Cancel",
      invalidName: "Invalid name length or content",
      projectAddSuccess: "Project: {name} added successfully",
      projectAddFailed: "Project: {name} addition failed, reason:",
      configureAddSuccess: "Configuration: {name} added successfully",
      configureAddFailed: "Configuration: {name} addition failed, reason:",
      renameSuccess: "Rename successful",
      renameFailed: "Rename failed, reason:",
      confirmDelete: "Are you sure to delete?",
      confirmBatchDelete: "This project has associated configuration content, are you sure to batch delete?",
      deleteSuccess: "Delete successful",
      deleteFailed: "Delete failed, reason:"
    },
    configures: {
      customComponent: "Custom Component",
      selectDevice: "Please select associated device!",
      edit: "Edit:"
    },
    customConfigure: {
      getCustomDeviceFailed: "Failed to get custom device",
      saveDeviceFailed: "Failed to save device symbol",
      saveSuccess: "Save successful",
      deleteDeviceFailed: "Failed to delete device symbol",
      deleteSuccess: "Delete successful",
      tip: "Tip"
    },
    deviceList: {
      unnamed: "Unnamed Device",
      connect: "Connect",
      disconnect: "Disconnect",
      edit: "Edit",
      delete: "Delete",
      notFound: "No device found",
      editWarn: "Please disconnect before editing",
      deleteWarn: "Please disconnect before deleting",
      connectSuccess: "Device {name}: Connected successfully",
      connectExist: "Device {name}: Connection already exists",
      connectFailed: "Device {name}: Connection failed",
      connectFailedReason: "Device connection failed reason: {reason}",
      disconnectSuccess: "Device {name}: Disconnected",
      operateFailed: "Device {name}: Operation failed",
      edit: "Edit",
      delete: "Delete",
      remove: "Remove"
    },
    deviceSearch: {
      searchPlaceholder: "Search device"
    },
    editConfigure: {
      saveSuccess: "Save successful",
      saveFailed: "Save failed, reason:",
      loadFailed: "Loading failed, reason:",
      getCustomDeviceFailed: "Failed to get custom device",
      tip: "Tip"
    },
    remoteSet: {
      inputValue: "Input Value",
      write: "Write",
      cancel: "Cancel",
      setFailed: "Remote set failed, reason:",
      operateSuccess: "Operation successful",
      noSetType: "Remote set type not configured"
    }
  },
  graphDefine: {
    equipmentList: {
      sequence: "Index",
      name: "Name",
      type: "Type",
      operation: "Operation",
      preview: "Preview",
      copy: "Copy",
      delete: "Delete",
      confirmDelete: "Are you sure to delete?",
      tip: "Tip",
      error: "Error"
    },
    graphComponent: {
      deviceType: "Device Type",
      deviceName: "Device Name",
      save: "Save"
    },
    contextMenu: {
      group: "Group",
      ungroup: "Ungroup",
      setStatus: "Set Status",
      copy: "Copy",
      delete: "Delete",
      rename: "Rename"
    },
    graphCreate: {
      needTwoDevices: "Switch or disconnector requires two device symbols selected",
      needCorrectStatus: "Please set the correct status property for the switch or disconnector",
      needOneDevice: "Please select a device symbol"
    },
    graphDefine: {
      waitCanvasInit: "Please wait for canvas initialization to complete",
      selectOneGraph: "Please select a symbol",
      tip: "Tip"
    },
    setStatus: {
      open: "Open",
      close: "Close",
      none: "None"
    },
    graphTools: {
      undo: "Undo",
      redo: "Redo",
      front: "Bring to Front",
      back: "Send to Back",
      delete: "Delete",
      save: "Save"
    },
    graphEditor: {
      dataConfig: "Data Configuration",
      loadEquipmentFailed: "Failed to load symbol"
    }
  },
  graph: {
    component: {
      electricSymbols: "Electric Symbols",
      customComponents: "Custom Components",
      basicComponents: "Basic Components"
    },
    toolbar: {
      undo: "Undo",
      redo: "Redo",
      bringToFront: "Bring to Front",
      sendToBack: "Send to Back",
      ratio: "Proportional Scaling",
      delete: "Delete",
      save: "Save"
    },
    contextMenu: {
      group: "Group",
      ungroup: "Ungroup",
      linkData: "Link Data",
      equipmentSaddr: "Device Address"
    },
    dialog: {
      dataConfig: "Data Configuration",
      tip: "Tip",
      selectOneGraph: "Please select a symbol"
    },
    message: {
      waitForCanvasInit: "Please wait for canvas initialization to complete",
      loadEquipmentFailed: "Failed to load symbol",
      loadEquipmentError: "Failed to load device",
      equipmentLoaded: "Device loaded"
    },
    basic: {
      title: "Basic Components",
      components: {
        line: "Line",
        text: "Text",
        rectangle: "Rectangle",
        circle: "Circle",
        ellipse: "Ellipse",
        triangle: "Triangle",
        arc: "Arc"
      }
    },
    selectEquipment: {
      sequence: "Index",
      name: "Name",
      type: "Type",
      symbol: "Symbol",
      operation: "Operation",
      reference: "Reference"
    },
    setSAddr: {
      telemetry: "Remote Sensing/Remote Measurement",
      format: "Formatting",
      factor: "Factor",
      remoteControl: "Remote Control",
      controlType: "Remote Control Method",
      controlValue: "Remote Control Value",
      remoteSet: "Remote Set",
      setType: "Remote Set Method",
      displayConfig: "Display Configuration",
      addRow: "Add Row",
      sequence: "Index",
      type: "Type",
      originalValue: "Original Value",
      displayValue: "Display Value",
      operation: "Operation",
      text: "Text",
      symbol: "Symbol",
      selectSymbol: "Select Symbol",
      confirm: "Confirm",
      cancel: "Cancel",
      confirmDelete: "Are you sure to delete?",
      tip: "Tip",
      selectControl: "Select Control",
      directControl: "Direct Control",
      controlClose: "Control Close",
      controlOpen: "Control Open",
      cancelDelete: "Cancel Delete"
    },
    equipmentType: {
      CBR: "Circuit Breaker",
      DIS: "Isolation Disconnector",
      GDIS: "Ground Disconnector",
      PTR2: "2-Winding Transformer",
      PTR3: "3-Winding Transformer",
      VTR: "Voltage Transformer",
      CTR: "Current Transformer",
      EFN: "Neutral Grounding Device",
      IFL: "Outlet",
      EnergyConsumer: "Load",
      GND: "Ground",
      Arrester: "Surge Arrester",
      Capacitor_P: "Parallel Capacitor",
      Capacitor_S: "Series Capacitor",
      Reactor_P: "Parallel Reactor",
      Reactor_S: "Series Reactor",
      Ascoil: "Arc Suppression Coil",
      Fuse: "Fuse",
      BAT: "Battery",
      BSH: "Capacitor",
      CAB: "Cable",
      LIN: "Overhead Line",
      GEN: "Generator",
      GIL: "Electrical Insulation Line",
      RRC: "Rotating Reactive Component",
      TCF: "Thyristor Controlled Frequency Converter",
      TCR: "Thyristor Controlled Reactive Component",
      LTC: "Tap Changer",
      IND: "Inductor"
    },
    equipmentName: {
      breaker_vertical: "Circuit Breaker-Vertical",
      breaker_horizontal: "Circuit Breaker-Horizontal",
      breaker_invalid_vertical: "Circuit Breaker-Invalid-Vertical",
      breaker_invalid_horizontal: "Circuit Breaker-Invalid-Horizontal",
      disconnector_vertical: "Disconnector-Vertical",
      disconnector_horizontal: "Disconnector-Horizontal",
      disconnector_invalid_vertical: "Disconnector-Invalid-Vertical",
      disconnector_invalid_horizontal: "Disconnector-Invalid-Horizontal",
      hv_fuse: "High Voltage Fuse",
      station_transformer_2w: "Station Transformer (Two Winding)",
      transformer_y_d_11: "Transformer (Y/△-11)",
      transformer_d_y_11: "Transformer (△/Y-11)",
      transformer_d_d: "Transformer (△/△)",
      transformer_y_y_11: "Transformer (Y/Y-11)",
      transformer_y_y_12_d_11: "Transformer (Y/Y-12/△-11)",
      transformer_y_d_11_d_11: "Transformer (Y/△-11/△-11)",
      transformer_y_y_v: "Transformer (Y/Y/V)",
      transformer_autotransformer: "Transformer (Autotransformer)",
      voltage_transformer_2w: "Voltage Transformer (Two Winding)",
      voltage_transformer_3w: "Voltage Transformer (Three Winding)",
      voltage_transformer_4w: "Voltage Transformer (Four Winding)",
      arrester: "Surge Arrester",
      capacitor_horizontal: "Capacitor-Horizontal",
      capacitor_vertical: "Capacitor-Vertical",
      reactor: "Reactor",
      split_reactor: "Split Reactor",
      power_inductor: "Power Inductor",
      feeder: "Outlet",
      ground: "Ground",
      tap_changer: "Tap Changer",
      connection_point: "Connection Point",
      transformer_y_y_12_d_11_new: "Transformer (Y/Y-12/△-11)(New)",
      pt: "PT",
      arrester_new: "Surge Arrester(New)",
      disconnector_vertical_new: "Disconnector-Vertical(New)",
      disconnector_horizontal_new: "Disconnector-Horizontal(New)",
      arrester_new_vertical: "Surge Arrester(New)-Vertical",
      disconnector_vertical_left_new: "Disconnector-Vertical Left(New)"
    }
  },
  graphProperties: {
    blank: {
      propertySetting: "Property Setting"
    },
    graph: {
      canvasSetting: "Canvas Setting",
      grid: "Grid",
      backgroundColor: "Background Color"
    },
    group: {
      groupProperty: "Group Property",
      basic: "Basic",
      width: "Width",
      height: "Height",
      x: "Position(X)",
      y: "Position(Y)",
      angle: "Rotation Angle"
    },
    node: {
      nodeProperty: "Node Property",
      style: "Style",
      backgroundColor: "Background Color",
      borderWidth: "Border Width",
      borderColor: "Border Color",
      borderDasharray: "Border Style",
      rx: "Border rx",
      ry: "Border ry",
      position: "Position",
      width: "Width",
      height: "Height",
      x: "Position(X)",
      y: "Position(Y)",
      property: "Property",
      angle: "Rotation Angle",
      zIndex: "Level(z)",
      fontFamily: "Font",
      fontColor: "Font Color",
      fontSize: "Font Size",
      text: "Text"
    },
    pathLine: {
      lineSetting: "Line Setting",
      style: "Style",
      lineHeight: "Width",
      lineColor: "Color",
      borderDasharray: "Border",
      position: "Position",
      width: "Width",
      height: "Height",
      x: "Position(X)",
      y: "Position(Y)",
      property: "Property",
      angle: "Rotation Angle",
      zIndex: "Level(z)"
    }
  },
  business: {
    hmi: {
      title: "Screen Management",
      form: {
        add: "Add Screen",
        edit: "Edit Screen",
        view: "View Screen",
        name: "Screen Name",
        type: "Screen Type",
        template: "Screen Template",
        description: "Description",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          name: "Please enter screen name",
          type: "Please select screen type",
          template: "Please select screen template"
        }
      },
      columns: {
        name: "Screen Name",
        type: "Screen Type",
        template: "Screen Template",
        createTime: "Create Time",
        updateTime: "Update Time",
        status: "Status",
        operation: "Operation"
      },
      type: {
        device: "Device Screen",
        process: "Process Screen",
        alarm: "Alarm Screen",
        custom: "Custom Screen"
      },
      status: {
        draft: "Draft",
        published: "Published",
        archived: "Archived"
      },
      editor: {
        title: "Screen Editing",
        save: "Save",
        preview: "Preview",
        publish: "Publish",
        cancel: "Cancel",
        tools: {
          select: "Select",
          rectangle: "Rectangle",
          circle: "Circle",
          line: "Line",
          text: "Text",
          image: "Image",
          device: "Device",
          alarm: "Alarm",
          chart: "Chart"
        },
        properties: {
          title: "Property",
          position: "Position",
          size: "Size",
          style: "Style",
          data: "Data",
          event: "Event"
        }
      },
      preview: {
        title: "Screen Preview",
        fullscreen: "Full Screen",
        exit: "Exit",
        zoom: {
          in: "Zoom In",
          out: "Zoom Out",
          fit: "Fit"
        }
      },
      publish: {
        title: "Publish Screen",
        version: "Version Number",
        description: "Publish Description",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          version: "Please enter version number",
          description: "Please enter publish description"
        }
      },
      template: {
        title: "Screen Template",
        add: "Add Template",
        edit: "Edit Template",
        delete: "Delete Template",
        name: "Template Name",
        category: "Template Category",
        description: "Description",
        preview: "Preview",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          name: "Please enter template name",
          category: "Please select template category"
        }
      }
    }
  }
};
